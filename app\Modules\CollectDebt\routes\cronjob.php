<?php

use Illuminate\Support\Facades\Route; 
use App\Http\Middleware\EnableSettingMiddleware;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtGuide\CollectDebtGuideController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CanhBaoTrichNgayController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary\CollectDebtSummaryController;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\GenFeeReportAction\GenFeeReportAction;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtLedger\CollectDebtLedgerReportController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtSchedule\CollectDebtUpcomingPlanController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtSummary\CollectDebtSummaryRefundController;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\GenFeeReportAction\MoveLateFeeToGenFeeAction;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtWaitProcess\CollectDebtWaitProcessController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtContractEvent\CollectDebtContractEventController;
use App\Modules\CollectDebt\Controllers\v1\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowController;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\ThucHienCaiTienGhiSoAction\LedgerRecordBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\HachToanYeuCauTrenSoAction\AccountingLedgerBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtPartner\DebtRecoveryPartnerCheckAction\PartnerCheckBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestRecheckAction\RecheckRequestBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestSendPaymentAction\SendRequestBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCompleteAction\SettleContractBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\AlertStuckRequestAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\CutOffRequestBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtSummary\DebtRecoverySummaryCheckLedgerExsistAction\SyncContractBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestCheckPaymentAction\CheckRequestPaymentBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoveryLedgerAccountingSummaryAction\AccountingSummaryBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtNotifyDebtNow\CollectDebtNotifyDebtNowSendMailAction\SendNotifyViaChannelsAction;
use App\Modules\CollectDebt\Actions\CollectDebtSchedule\DebtRecoveryContractPlanCreateRequestAction\CreateRequestAutoBatchAction;
use App\Modules\CollectDebt\Actions\CollectDebtLedger\DebtRecoverySendBaoCaoTrichNoTnexAction\DebtRecoverySendBaoCaoTrichNoTnexAction;
use App\Modules\CollectDebt\Actions\CollectDebtRequest\DebtRecoveryRequestFinishCutOffTimeAction\DebtRecoveryRequestFinishCutOffTimeAction;


// CronJob: Tạo lịch thu hồi từ chỉ dẫn rồi bắn vào api /DebtRecoveryContractPlanCreate 
Route::any('/DebtRecoveryContractGuideCreatePlan', [
  'as' => 'DebtRecoveryContractGuideCreatePlanAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtGuide\CollectDebtGuideController@createPlan'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_TAO_LICH_TU_CHI_DAN'));

/*------------------- CronJob tạo lệnh trích tự động từ lịch thu--------------------*/
	Route::any('/CreateRequestAutoBatchAction', function () {
		return app(CreateRequestAutoBatchAction::class)->run();
	});

	Route::any('/HandleCreateRequestAuto', function () {
		return app(CreateRequestAutoBatchAction::class)->HandleCreateRequestAuto();
	});
/*------------------- ./End CronJob tạo lệnh trích tự động từ lịch thu--------------------*/

Route::any('/DebtRecoveryContractCreateRequestViaWallet', [
  'as' => 'DebtRecoveryContractCreateRequestViaWalletAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtSchedule\CollectDebtScheduleRequestController@createRequestViaWallet'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_TAO_YEU_CAU_TU_LICH'));

/*-------------------Gửi lệnh trích sang mpos--------------------*/
	
	Route::any('/SendRequestBatchAction', function () {
		return app(SendRequestBatchAction::class)->run();
	});

	Route::any('/HandleSendRequestMpos', function () {
		return app(SendRequestBatchAction::class)->HandleSendRequestMpos();
	});
/*-------------------./End Gửi lệnh trích sang mpos--------------------*/

/*------------------- CronJob kiểm tra kết quả trích nợ--------------------*/
	
	Route::any('/CheckRequestPaymentBatchAction', function () {
		return app(CheckRequestPaymentBatchAction::class)->run();
	});

	Route::any('/HandleCheckMposRequest', function () {
		return app(CheckRequestPaymentBatchAction::class)->HandleCheckMposRequest();
	});
/*------------------- ./ End CronJob kiểm tra kết quả trích nợ--------------------*/

// Cronjob thực hiện giảm trừ số tiền đóng băng và giảm số dư ví sau khi lệnh trích đã đc hạch toán
Route::any('/DebtRecoveryRequestOpenAndMinusFreeze', [
  'as' => 'DebtRecoveryRequestOpenAndMinusFreezeAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequest\CollectDebtRequestController@DebtRecoveryRequestOpenAndMinusFreeze'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_GIAM_TRU_SO_DU_VI_VA_TIEN_DONG_BANG_TRONG_VI'));

/*------------------- Cronjob xử lý Partner tiền về --------------------*/
	
	Route::any('/PartnerCheckBatchAction', function () {
		return app(PartnerCheckBatchAction::class)->run();
	});

	Route::any('/HandlePartnerCheck/{id}', function ($id) {
		return app(PartnerCheckBatchAction::class)->HandlePartnerCheck($id);
	});
/*------------------- ./ End Cronjob xử lý Partner tiền về --------------------*/


/*------------------- Ghi sổ --------------------*/
	
	Route::any('/LedgerRecordBatchAction', function () {
		return app(LedgerRecordBatchAction::class)->run();
	});

	Route::any('/HandleLedgerRecord/{partnerRequestId}', function (string $partnerRequestId) {
		return app(LedgerRecordBatchAction::class)->HandleLedgerRecord($partnerRequestId);
	});
/*-------------------./ End ghi sổ --------------------*/

/*------------------- Cronjob xử lý CutOff Time --------------------*/
	
	Route::any('/CutOffRequestBatchAction', function () {
		return app(CutOffRequestBatchAction::class)->run();
	});

	Route::any('/HandleCutOffRequest', function () {
		return app(CutOffRequestBatchAction::class)->HandleCutOffRequest();
	});
/*------------------- ./End Cronjob xử lý CutOff Time --------------------*/

/*------------------- Hạch toán --------------------*/
	Route::any('/AccountingLedgerBatchAction', function () {
		return app(AccountingLedgerBatchAction::class)->run();
	});

	Route::any('/HandleAccoutingLedger/{id}', function ($id) {
		return app(AccountingLedgerBatchAction::class)->HandleAccoutingLedger($id);
	});
/*------------------- ./End Hạch toán --------------------*/

/*-------------------Đẩy bảng tổng hợp--------------------*/
	Route::any('/AccountingSummaryBatchAction', function () {
		return app(AccountingSummaryBatchAction::class)->run();
	});

	Route::any('/HandleAccoutingSummary/{id}', function ($id) {
		return app(AccountingSummaryBatchAction::class)->HandleAccoutingSummary($id);
	});
/*-------------------./Đẩy bảng tổng hợp--------------------*/


// Job xu ly mail sap toi ky
Route::any('/DebtRecoveryHandleFirstUpcomingPlan', [
  'as' => 'DebtRecoveryHandleFirstUpcomingPlanAction',
  'uses' => CollectDebtUpcomingPlanController::class . '@DebtRecoveryHandleFirstUpcomingPlan'
]);

Route::any('/DebtRecoveryHandleSapToiHan', [
  'as' => 'DebtRecoveryHandleSapToiHanAction',
  'uses' => CollectDebtUpcomingPlanController::class . '@DebtRecoveryHandleSapToiHan'
]);

// Đến kỳ thanh toán
Route::any('/DebtRecoveryHandleMailDenKyDungNgay', [
  'as' => 'DebtRecoveryHandleMailDenKyDungNgayAction',
  'uses' => CollectDebtUpcomingPlanController::class . '@DebtRecoveryHandleMailDenKyDungNgay'
]);

// Đến hạn tất toán
Route::any('/DebtRecoveryHandleDenHanTatToanDungNgay', [
  'as' => 'DebtRecoveryHandleDenHanTatToanDungNgayAction',
  'uses' => CollectDebtUpcomingPlanController::class . '@DebtRecoveryHandleDenHanTatToanDungNgay'
]);

// Job duyệt tự động chỉ dẫn
Route::any('/DebtRecoveryGuideAutoApproved', [
  'as' => 'DebtRecoveryGuideAutoApproved',
  'uses' => CollectDebtGuideController::class . '@autoAprroved'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_DUYET_CHI_DAN_TU_DONG'));


// Thực hiện xử lý yêu cầu điều chỉnh và mở lại lịch
Route::any('/DebtRecoveryRequestAdjustmentProcess', [
  'as' => 'DebtRecoveryRequestAdjustmentProcessAction',
  'uses' => 'App\Modules\CollectDebt\Controllers\v1\CollectDebtRequestAdjustment\CollectDebtRequestAdjustmentController@process'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_XU_LY_YEU_CAU_DIEU_CHINH'));


/*-------------------Tất toán hợp đồng-------------------*/
	Route::any('/SettleContractBatchAction', function () {
		return app(SettleContractBatchAction::class)->run();
	});

	Route::any('/HandleSettleContract/{contractCode}', function (string $contractCode) {
		return app(SettleContractBatchAction::class)->HandleSettleContract($contractCode);
	});
/*-------------------./End Tất toán hợp đồng-------------------*/

/*-------------------Recheck lại lệnh trích gọi hủy lúc cutoff không được-------------------*/
	Route::any('/RecheckRequestBatchAction', function () {
		return app(RecheckRequestBatchAction::class)->run();
	});

	Route::any('/HandleRecheckRequest/{partnerRequestId}', function (string $partnerRequestId) {
		return app(RecheckRequestBatchAction::class)->HandleRecheckRequest($partnerRequestId);
	});
/*-------------------./ End Recheck lại lệnh trích gọi hủy lúc cutoff không được-------------------*/

// Job turn on dong bo tong hop ve he thong hop dong
Route::any('/MarkAsNeedSyncAt3h', [
  'as' => 'MarkAsNeedSyncAt3h',
  'uses' => CollectDebtSummaryController::class . '@MarkAsNeedSyncAt3h'
]);

/*-------------------Đồng bộ kết quả trích nợ về service hợp đồng----------------- */
	Route::any('/SyncContractBatchAction', function () {
		return app(SyncContractBatchAction::class)->run();
	});

	Route::any('/HandleSyncContract/{contractCode}', function (string $contractCode) {
		return app(SyncContractBatchAction::class)->handleSyncContract($contractCode);
	});
/*-------------------./End Đồng bộ kết quả trích nợ về service hợp đồng----------------- */

// danh dau huy yc mpos khi co tien ve tu IB_OFF hoac VA
Route::any('/DebtRecoveryWaitProcessHandle', [
  'as' => 'DebtRecoveryWaitProcessHandleAction',
  'uses' => CollectDebtWaitProcessController::class . '@processHandle'
])->middleware(sprintf('%s:%s',EnableSettingMiddleware::class, 'JOB_DONG_BO_HANG_NGAY'));

// Mail qua han 3 cap do
Route::any('/DebtRecoverySummaryHandleMailOverdue', [
  'as' => 'DebtRecoverySummaryMailOverDueAction',
  'uses' => CollectDebtSummaryController::class . '@handleMailOverdue'
]);

// Mail cham ky
Route::any('/DebtRecoverySummaryHandleMailOverCycle', [
  'as' => 'DebtRecoverySummaryMailOverCycleAction',
  'uses' => CollectDebtSummaryController::class . '@handleMailOverCycle'
]);

// Xoa cac ban ghi event cu
Route::any('/DebtRecoveryContractDeleteOldEvent', [
  'as' => 'DebtRecoveryContractDeleteOldEvent',
  'uses' => CollectDebtContractEventController::class . '@deleteOldEvent'
]);

// api thuc hien check hoan tien cho: luong thu thua & luong hoan phi
Route::any('/DebtRecoverySummaryCheckRefund', [
	'as' => 'DebtRecoverySummaryCheckRefundAction',
	'uses' => CollectDebtSummaryRefundController::class . '@DebtRecoverySummaryCheckRefund'
]);

// Job kiểm tra lệnh trích ngay, nếu trích ko đủ tiền sẽ báo về email
Route::any('/DebtRecoveryRequestCanhBaoTrichNgay', [
	'as' => 'DebtRecoveryRequestCanhBaoTrichNgayAction',
	'uses' => CanhBaoTrichNgayController::class . '@handler'
]);

// Job gửi mail thông báo về tình trạng lệnh trích ngay
Route::any('/DebtRecoveryJobNotifyDebtNow', [
	'as' => SendNotifyViaChannelsAction::class,
	'uses' => CollectDebtNotifyDebtNowController::class . '@jobNotifyDebtNow'
]);

// Job báo cáo giao dịch trích nợ về cho bên HĐ làm việc với Tnex
Route::any('/DebtRecoverySendBaoCaoTrichNoTnex', [
	'as' => DebtRecoverySendBaoCaoTrichNoTnexAction::class,
	'uses' => CollectDebtLedgerReportController::class . '@sendReportTrichNoTnex'
]);

// Cảnh báo tắc lệnh trích
Route::any('/AlertStuckRequestAction', function () {
	return app(AlertStuckRequestAction::class)->run();
});

// Đưa thông tin sinh phí từ sổ --> vào bảng sinh phí
Route::any('/GenFeeReportAction', function () {
	return app(GenFeeReportAction::class)->run();
});

